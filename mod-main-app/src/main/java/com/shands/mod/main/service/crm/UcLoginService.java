package com.shands.mod.main.service.crm;

import com.shands.mod.dao.model.v0701.dto.UcLoginDto;
import com.shands.mod.dao.model.v0701.vo.UcUserInfoByMobileVo;
import com.shands.mod.vo.UserInfoVO;
import com.shands.uc.model.req.v3.auth.UserAuthInfo;

/**
 * <AUTHOR>
 * @date 2021/6/8
 * @desc 通宝登录service
*/
public interface UcLoginService {

  /**
   * 通宝token登录
   * @param ucLoginDto token
   * @return
   */
  UserInfoVO ucLogin(UcLoginDto ucLoginDto);

  /**
   * 通宝token登录
   * @return
   */
  UserInfoVO ucLoginById(Integer ucId);

  /**
   * 设置用户菜单权限信息
   * @param result
   * @param userAuthInfo
   * @return
   */
  void getRights(UserInfoVO result,UserAuthInfo userAuthInfo,String plantform);

  /**
   * 更新人员信息
   * @param userAuthInfo
   * @return
   */
  boolean updUcUserInfo(UserAuthInfo userAuthInfo);

  /**
   * 通过手机号查询通宝用户信息
   * @param mobile 手机号
   * @return UC用户信息
   */
  UcUserInfoByMobileVo queryByMobile(String mobile);
}
