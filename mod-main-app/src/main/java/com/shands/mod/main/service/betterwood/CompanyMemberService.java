package com.shands.mod.main.service.betterwood;

import com.github.pagehelper.PageInfo;
import com.shands.mod.dao.model.sales.tool.domain.ModCompanyApplyRecord;
import com.shands.mod.dao.model.sales.tool.req.CompanyApplyReq;
import com.shands.mod.dao.model.sales.tool.req.CompanyMemberPageListReq;
import com.shands.mod.dao.model.sales.tool.req.CompanyPageListReq;
import com.shands.mod.dao.model.sales.tool.res.*;
import com.shands.mod.main.service.betterwood.rpc.resp.CompanyVO;
import org.springframework.web.multipart.MultipartFile;
import java.util.List;

public interface CompanyMemberService {

  PageInfo<CompanyPageListRes> queryCompanyPageList(CompanyPageListReq req);

  PageInfo<CompanyMemberPageListRes> queryMemberPageList(CompanyMemberPageListReq req);

  UploadBizLicenseRes bizLicenseUpload(MultipartFile file, Integer uploadType);

  PublicEmailRes getPublicEmailSuffix();

  CompanyVO companyApply(CompanyApplyReq req);

  PageInfo<ModCompanyApplyRecord> getCompanyApplyRecordPage(Integer pageNo, Integer pageSize);

  CompanyContractRes queryContractByCompanyId(Long companyId);
}
