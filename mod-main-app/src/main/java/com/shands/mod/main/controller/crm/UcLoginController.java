package com.shands.mod.main.controller.crm;

import com.shands.mod.dao.model.v0701.dto.UcLoginByIdDto;
import com.shands.mod.dao.model.v0701.dto.UcLoginDto;
import com.shands.mod.main.service.crm.UcLoginService;
import com.shands.mod.vo.ResultVO;
import com.shands.mod.vo.UserInfoVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import com.betterwood.log.core.enums.MethodTypeEnum;
import com.betterwood.log.core.annotation.ResultLog;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = "/ucUser")
@Api(value = "用户登录中心",tags = "酒店管理后台")
@Slf4j
public class UcLoginController {

  private final UcLoginService ucLoginService;

  public UcLoginController(UcLoginService ucLoginService) {
    this.ucLoginService = ucLoginService;
  }

  @PostMapping(value = "/ucLogin")
  @ApiOperation(value = "用户登录中心-通宝token登录")
    @ResultLog(name = "UcLoginController.ucLogin", methodType = MethodTypeEnum.HTTP_UP)
  public ResultVO<UserInfoVO> ucLogin(@RequestBody UcLoginDto ucLoginDto){
    return ResultVO.success(ucLoginService.ucLogin(ucLoginDto));
  }


  @PostMapping(value = "/ucLoginById")
  @ApiOperation(value = "用户登录中心-通宝token登录(通过ucId)")
  @ResultLog(name = "UcLoginController.ucLoginById", methodType = MethodTypeEnum.HTTP_UP)
  public ResultVO<UserInfoVO> ucLoginById(@RequestBody UcLoginByIdDto ucLoginByIdDto){
    return ResultVO.success(ucLoginService.ucLoginById(ucLoginByIdDto.getUcId()));
  }
}
